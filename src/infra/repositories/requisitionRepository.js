const BaseRepository = require('./baseRepository');

class RequisitionRepository extends BaseRepository {
  constructor({
    db,
    clientErrors,
    utils,
    constants,
    entities,
    notificationService,
    canvassRequisitionRepository,
    purchaseOrderRepository,
  }) {
    super(db.requisitionModel);
    this.clientErrors = clientErrors;
    this.Sequelize = db.Sequelize;
    this.db = db;
    this.utils = utils;
    this.requisitionItemListModel = db.requisitionItemListModel;
    this.notificationService = notificationService;
    this.constants = constants;
    this.entities = entities;
    this.canvassRequisitionRepository = canvassRequisitionRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
  }

  async getAllRequisitions(payload) {
    const { REQUISITION_STATUS } = this.constants.requisition;

    let whereClause = {};
    let whereRequestor = {};
    let whereCompany = {};
    let whereDepartment = {};
    let whereProject = {};
    let whereView = {};
    let isRequired = false;

    const { limit, filterBy, page, requestType, userId } = payload;

    const {
      type,
      requestor,
      company,
      department,
      project,
      status,
      rsNumber: searchValue,
    } = this.utils.buildFilterWhereClause(filterBy);

    if (searchValue) {
      // this is a special case where we want to search for both the draft RS number
      // and the regular RS number. We need to use a literal string to allow for
      // concatenation of the columns. We also need to handle the case where the
      // user is searching for a draft RS number, in which case we need to filter
      // by the status of the requisition
      isRequired = true;
      const trimSearchValue = searchValue.trim();

      whereView[this.Sequelize.Op.or] = [
        {
          rsCombinedNumber: {
            [this.Sequelize.Op.iLike]: `%${trimSearchValue}%`,
          },
        },
        {
          canvassNumber: { [this.Sequelize.Op.iLike]: `%${trimSearchValue}%` },
        },
        { poNumber: { [this.Sequelize.Op.iLike]: `%${trimSearchValue}%` } },
        { prNumber: { [this.Sequelize.Op.iLike]: `%${trimSearchValue}%` } },
        { drNumber: { [this.Sequelize.Op.iLike]: `%${trimSearchValue}%` } },
        { refType: { [this.Sequelize.Op.iLike]: `${trimSearchValue}%` } },
        {
          requestorFullName: {
            [this.Sequelize.Op.iLike]: `%${trimSearchValue}%`,
          },
        },
        { companyName: { [this.Sequelize.Op.iLike]: `%${trimSearchValue}%` } },
        { projectName: { [this.Sequelize.Op.iLike]: `%${trimSearchValue}%` } },
        {
          departmentName: { [this.Sequelize.Op.iLike]: `%${trimSearchValue}%` },
        },
        { refStatus: { [this.Sequelize.Op.iLike]: `%${trimSearchValue}%` } },
      ];
    }

    if (type) {
      whereClause.type = { [this.Sequelize.Op.like]: `${type}%` };
    }

    if (requestor) {
      whereRequestor[this.Sequelize.Op.or] = [
        this.Sequelize.where(
          this.Sequelize.fn(
            'concat',
            this.Sequelize.col('first_name'),
            ' ',
            this.Sequelize.col('last_name'),
          ),
          { [this.Sequelize.Op.iLike]: `%${requestor}%` },
        ),
      ];
    }

    if (company) {
      whereCompany.name = {
        [this.Sequelize.Op.iLike]: `%${company}%`,
      };
    }

    if (department) {
      whereDepartment.name = {
        [this.Sequelize.Op.iLike]: `%${department}%`,
      };
    }

    if (project) {
      whereProject.name = {
        [this.Sequelize.Op.iLike]: `%${project}%`,
      };
    }

    if (status) {
      whereClause.status = {
        [this.Sequelize.Op.iLike]: `%${status}%`,
      };
    }

    if (requestType === 'my_request') {
      whereClause.createdBy = userId;
    }

    if (requestType === 'my_approval') {
      // get all requisitions where the user is either the assignedTo or is an approver
      whereClause[this.Sequelize.Op.or] = [
        { assignedTo: userId },
        {
          id: {
            [this.Sequelize.Op.in]: this.Sequelize.literal(
              `(SELECT requisition_id FROM ${this.db.requisitionApproverModel.tableName} WHERE approver_id = ${userId})`,
            ),
          },
        },
      ];
    }

    // TODO: implement history of requisitions

    const formattedOrder = [];
    for (const [field, direction] of Object.entries(
      payload.order ?? { updatedAt: 'DESC' },
    )) {
      switch (field) {
        case 'rsNumber':
          formattedOrder.push([
            this.Sequelize.literal('"combine_rs_number"'),
            direction.toUpperCase(),
          ]);
          continue;
        case 'company':
          formattedOrder.push([
            this.Sequelize.literal('"company.name"'),
            direction.toUpperCase(),
          ]);
          continue;
        case 'project':
          formattedOrder.push([
            this.Sequelize.literal('"project.name"'),
            direction.toUpperCase(),
          ]);
          continue;
        case 'department':
          formattedOrder.push([
            this.Sequelize.literal('"department.name"'),
            direction.toUpperCase(),
          ]);
          continue;
        case 'requestor':
          formattedOrder.push([
            this.Sequelize.literal(`"createdByUser.first_name"`),
            direction.toUpperCase(),
          ]);
          formattedOrder.push([
            this.Sequelize.literal(`"createdByUser.last_name"`),
            direction.toUpperCase(),
          ]);
          continue;
        case 'type':
          formattedOrder.push(['type', direction.toUpperCase()]);
          continue;
        case 'createdAt':
          formattedOrder.push(['created_at', direction.toUpperCase()]);
          continue;
        case 'updatedAt':
          formattedOrder.push(['updated_at', direction.toUpperCase()]);
          continue;
        case 'status':
          formattedOrder.push(['status', direction.toUpperCase()]);
          continue;
        default:
          continue;
      }
    }

    return await this.findAll({
      limit,
      page,
      order: formattedOrder,
      where: whereClause,
      attributes: [
        'id',
        [
          this.Sequelize.fn(
            'CONCAT',
            this.Sequelize.literal(
              "CASE WHEN \"requisitions\".\"status\" = 'rs_draft' THEN 'TMP-' ELSE '' END",
            ),
            this.Sequelize.col('requisitions.company_code'),
            this.Sequelize.col('rs_letter'),
            this.Sequelize.fn(
              'COALESCE',
              this.Sequelize.col('rs_number'),
              this.Sequelize.col('draft_rs_number'),
            ),
          ),
          'combine_rs_number',
        ],
        [
          this.Sequelize.literal(
            `CASE WHEN type IN ('ofm-tom', 'non-ofm-tom') THEN CASE type WHEN 'ofm-tom' THEN 'OFM-TOM' WHEN 'non-ofm-tom' THEN 'Non-OFM-TOM' END WHEN type IN ('ofm', 'non-ofm') THEN CASE type WHEN 'ofm' THEN 'OFM' WHEN 'non-ofm' THEN 'Non-OFM' END ELSE type END`,
          ),
          'type',
        ],
        'date_required',
        'delivery_address',
        'purpose',
        'charge_to',
        'assigned_to',
        'status',
        [
          this.Sequelize.literal(
            `TO_CHAR("requisitions"."created_at", 'DD Mon YYYY')`,
          ),
          'date_requested',
        ],
        /*
         * TODO
         * g. Date Requested
         * i. Date the Requisition Slip was submitted (meaning when the requisition status was become "Submitted"? or just created?)
         * ii. Date Format: DD MMM YYYY (10 Jul 2023)
         * iii. Should display the Aging of the Request
         * i) Aging is computed by: From Date requested until Delivery
         * ii) Once requested should display zero as the Aging, and must increment by 1 once it is 12AM
         */
        [
          this.Sequelize.fn(
            'TO_CHAR',
            this.Sequelize.col('requisitions.updated_at'),
            'DD Mon YYYY',
          ),
          'last_updated',
        ],
        'created_at',
        'updated_at',
        'draft_rs_number',
        'rs_letter',
        'rs_number',
      ],
      include: [
        {
          model: this.db.companyModel,
          attributes: ['id', 'name'],
          as: 'company',
          where: whereCompany,
        },
        {
          model: this.db.projectModel,
          attributes: ['id', 'name'],
          as: 'project',
          where: whereProject,
        },
        {
          model: this.db.departmentModel,
          attributes: ['id', 'name'],
          as: 'department',
          where: whereDepartment,
        },
        {
          model: this.db.userModel,
          attributes: [
            'id',
            'first_name',
            'last_name',
            'username',
            [
              this.Sequelize.fn(
                'CONCAT',
                this.Sequelize.col('first_name'),
                ' ',
                this.Sequelize.col('last_name'),
              ),
              'fullName',
            ],
          ],
          as: 'createdByUser',
          where: whereRequestor,
        },
        {
          model: this.db.requisitionApproverModel,
          as: 'requisitionApprovers',
          attributes: ['id', 'approverId'],
        },
        // {
        //   model: this.db.requisitionBadgeModel,
        //   as: 'requisitionBadges',
        //   required: false,
        //   where: {
        //     [this.Sequelize.Op.not]: {
        //       seenBy: {
        //         [this.Sequelize.Op.contains]: [userId],
        //       },
        //     },
        //   },
        // },
        {
          model: this.db.purchaseOrderModel,
          as: 'purchaseOrders',
          required: false,
          attributes: [],
          include: [
            {
              attributes: [],
              model: this.db.purchaseOrderApproverModel,
              as: 'purchaseOrderApprovers',
              required: false,
              where: {
                userId: userId,
              },
            },
          ],
        },
        {
          model: this.db.viewDashboardModel,
          as: 'dashboardRequisition',
          where: whereView,
          required: isRequired,
          attributes: [],
        },
      ],
      distinct: true,
    });
  }

  async getAllRequisitionsV2(payload) {
    const {
      limit = 10,
      page = 1,
      order,
      filterBy,
      userFromToken,
      requestType,
    } = payload;

    const { id: userId, role } = userFromToken;

    const offset = (page - 1) * limit;

    let sortColumn = null;
    let sortDirection = 'DESC';

    if (order && Object.keys(order).length > 0) {
      const [field, directionInput] = Object.entries(order)[0];
      sortColumn = field;
      if (
        typeof directionInput === 'string' &&
        directionInput.toUpperCase() === 'ASC'
      ) {
        sortDirection = 'ASC';
      } else {
        sortDirection = 'DESC';
      }
    }

    const replacements = {
      userId,
      limit: parseInt(limit),
      offset: parseInt(offset),
    };

    const baseFilterConditions = [];

    if (filterBy?.ref_number) {
      const refNumGlobalSearchSubConditions = [
        `ud.ref_number ILIKE :ref_number_gsv`,
        `ud.doc_type ILIKE :ref_number_gsv`,
        `c.name ILIKE :ref_number_gsv`,
        `p.name ILIKE :ref_number_gsv`,
        `d.name ILIKE :ref_number_gsv`,
        `CONCAT(u.first_name, ' ', u.last_name) ILIKE :ref_number_gsv`,
        `ud.status ILIKE :ref_number_gsv`,
      ];

      const updatedAtSearchFormats = [
        `TO_CHAR(ud.updated_at, 'YYYY-MM-DD HH24:MI:SS') ILIKE :ref_number_gsv`, // Full ISO-like timestamp
        `TO_CHAR(ud.updated_at, 'YYYY-MM-DD') ILIKE :ref_number_gsv`, // ISO-like date
        `TO_CHAR(ud.updated_at, 'FMMonth DD, YYYY') ILIKE :ref_number_gsv`, // e.g., "June 4, 2025"
        `TO_CHAR(ud.updated_at, 'FMDD FMMonth YYYY') ILIKE :ref_number_gsv`, // e.g., "4 June 2025"
        `TO_CHAR(ud.updated_at, 'YYYY') ILIKE :ref_number_gsv`, // Year only, e.g., "2025"
        `TO_CHAR(ud.updated_at, 'FMMonth') ILIKE :ref_number_gsv`, // Full month name, e.g., "June"
        `TO_CHAR(ud.updated_at, 'FMDD') ILIKE :ref_number_gsv`, // Day of month, e.g., "4"
      ];
      refNumGlobalSearchSubConditions.push(
        `(${updatedAtSearchFormats.join(' OR ')})`,
      );

      baseFilterConditions.push(
        `(${refNumGlobalSearchSubConditions.join(' OR ')})`,
      );
      replacements.ref_number_gsv = `%${filterBy.ref_number}%`;
    }
    if (filterBy?.type) {
      baseFilterConditions.push(`ud.doc_type ILIKE :type`);
      replacements.type = filterBy.type;
    }
    if (filterBy?.company) {
      baseFilterConditions.push(`c.name ILIKE :company`);
      replacements.company = `%${filterBy.company}%`;
    }
    if (
      filterBy?.project_department &&
      typeof filterBy.project_department === 'string' &&
      filterBy.project_department.trim() !== ''
    ) {
      baseFilterConditions.push(
        `(d.name ILIKE :project_department_search_term OR p.name ILIKE :project_department_search_term)`,
      );
      replacements.project_department_search_term = `%${filterBy.project_department.trim()}%`;
    }
    if (filterBy?.requestor) {
      baseFilterConditions.push(
        `CONCAT(u.first_name, ' ', u.last_name) ILIKE :requestor`,
      );
      replacements.requestor = `%${filterBy.requestor}%`;
    }
    if (filterBy?.status) {
      baseFilterConditions.push(`ud.status ILIKE :status`);
      replacements.status = `%${filterBy.status}%`;
    }

    if (filterBy?.updated_at) {
      baseFilterConditions.push(
        `TO_CHAR(ud.updated_at, 'FMDD FMMonth YYYY') = :updated_at`,
      );
      replacements.updated_at = filterBy.updated_at;
    }

    if (
      filterBy?.statuses &&
      Array.isArray(filterBy.statuses) &&
      filterBy.statuses.length > 0
    ) {
      baseFilterConditions.push(`ud.status IN (:statuses)`);

      replacements.statuses = filterBy.statuses;
    }

    if (
      filterBy?.companies &&
      Array.isArray(filterBy.companies) &&
      filterBy.companies.length > 0
    ) {
      baseFilterConditions.push(`ud.company_id IN (:companies)`);

      replacements.companies = filterBy.companies;
    }

    const baseFilterClause =
      baseFilterConditions.length > 0
        ? `AND ${baseFilterConditions.join(' AND ')}`
        : '';

    const unifiedDocsCTE = `
    WITH unified_docs AS (
      SELECT
        r.id, 'requisition' AS doc_type,
        CASE
          WHEN r.status = 'rs_draft' THEN CONCAT('RS-TMP-', r.company_code, r.rs_letter, COALESCE(r.draft_rs_number, ''))
          ELSE CONCAT('RS-', r.company_code, r.rs_letter, COALESCE(r.rs_number, ''))
        END AS ref_number,
        r.created_by AS requestor_id, r.company_id, r.project_id, r.department_id,
        r.updated_at, r.status,
        CAST(r.id AS TEXT) AS grouping_id,
        r.status AS root_status,
        r.assigned_to AS assigned_to_user_id,
        (SELECT JSON_AGG(user_id) FROM (SELECT approver_id AS user_id FROM requisition_approvers WHERE requisition_id = r.id UNION SELECT alt_approver_id AS user_id FROM requisition_approvers WHERE requisition_id = r.id AND alt_approver_id IS NOT NULL) AS approvers) AS approvers
      FROM requisitions r
      UNION ALL
      SELECT
        cr.id, 'canvass' AS doc_type,
        CASE
          WHEN cr.cs_number IS NULL THEN CONCAT('CS-TMP-', r.company_code, cr.cs_letter, COALESCE(cr.draft_cs_number, ''))
          ELSE CONCAT('CS-', r.company_code, cr.cs_letter, COALESCE(cr.cs_number, ''))
        END AS ref_number,
        r.created_by AS requestor_id, r.company_id, r.project_id, r.department_id,
        cr.updated_at, cr.status,
        CAST(r.id AS TEXT) AS grouping_id,
        r.status AS root_status,
        r.assigned_to AS assigned_to_user_id,
        (SELECT JSON_AGG(user_id) FROM (SELECT user_id FROM canvass_approvers WHERE canvass_requisition_id = cr.id UNION SELECT alt_approver_id AS user_id FROM canvass_approvers WHERE canvass_requisition_id = cr.id AND alt_approver_id IS NOT NULL) AS approvers) AS approvers
      FROM canvass_requisitions cr JOIN requisitions r ON cr.requisition_id = r.id
      UNION ALL
      SELECT
        po.id, 'purchase_order' AS doc_type,
        CONCAT('PO-', r.company_code, po.po_letter, po.po_number) AS ref_number,
        r.created_by AS requestor_id, r.company_id, r.project_id, r.department_id,
        po.updated_at, po.status,
        CAST(r.id AS TEXT) AS grouping_id,
        r.status AS root_status,
       r.assigned_to AS assigned_to_user_id,
        (SELECT JSON_AGG(user_id) FROM (SELECT user_id FROM purchase_order_approvers WHERE purchase_order_id = po.id UNION SELECT alt_approver_id AS user_id FROM purchase_order_approvers WHERE purchase_order_id = po.id AND alt_approver_id IS NOT NULL) AS approvers) AS approvers
      FROM purchase_orders po JOIN requisitions r ON po.requisition_id = r.id
      UNION ALL
      SELECT
        dr.id, 'delivery_receipt' AS doc_type,
        CASE
          WHEN dr.is_draft THEN CONCAT('RR-TMP-', COALESCE(dr.draft_dr_number, ''))
          ELSE CONCAT('RR-', COALESCE(dr.dr_number, ''))
        END AS ref_number,
        r.created_by AS requestor_id, r.company_id, r.project_id, r.department_id,
        dr.updated_at, COALESCE(dr.status, '') AS status,
        CAST(r.id AS TEXT) AS grouping_id,
        r.status AS root_status,
        r.assigned_to AS assigned_to_user_id,
        NULL AS approvers
      FROM delivery_receipts dr JOIN requisitions r ON dr.requisition_id = r.id
      UNION ALL
      SELECT
        ir.id, 'invoice' AS doc_type,
        CASE
          WHEN ir.is_draft THEN CONCAT('IR-TMP-', COALESCE(ir.ir_draft_number, ''))
          ELSE CONCAT('IR-', COALESCE(ir.ir_number, ''))
        END AS ref_number,
        r.created_by AS requestor_id, r.company_id, r.project_id, r.department_id,
        ir.updated_at, ir.status,
        CAST(r.id AS TEXT) AS grouping_id,
        r.status AS root_status,
       r.assigned_to AS assigned_to_user_id,
        NULL AS approvers
      FROM invoice_reports ir JOIN requisitions r ON ir.requisition_id = r.id
      UNION ALL
      SELECT
        pr.id, 'payment_request' AS doc_type,
        CASE
          WHEN pr.is_draft THEN CONCAT('VR-TMP-', r.company_code, COALESCE(pr.draft_pr_number, ''))
          ELSE CONCAT('VR-', r.company_code, pr.pr_letter, COALESCE(pr.pr_number, ''))
        END AS ref_number,
        r.created_by AS requestor_id, r.company_id, r.project_id, r.department_id,
        pr.updated_at, pr.status,
        CAST(pr.requisition_id AS TEXT) AS grouping_id,
        r.status AS root_status,
        r.assigned_to AS assigned_to_user_id,
        (SELECT JSON_AGG(user_id) FROM (SELECT user_id FROM rs_payment_request_approvers WHERE payment_request_id = pr.id UNION SELECT alt_approver_id AS user_id FROM rs_payment_request_approvers WHERE payment_request_id = pr.id AND alt_approver_id IS NOT NULL) AS approvers) AS approvers
      FROM rs_payment_requests pr JOIN requisitions r ON pr.requisition_id = r.id
      UNION ALL
      SELECT
        nr.id, 'non_requisition' AS doc_type,
        CASE
          WHEN nr.status = 'draft' THEN CONCAT('NR-TMP-', nr.non_rs_letter, COALESCE(nr.draft_non_rs_number, ''))
          ELSE CONCAT('NR-', nr.non_rs_letter, COALESCE(nr.non_rs_number, ''))
        END AS ref_number,
        nr.created_by AS requestor_id, nr.company_id, nr.project_id, nr.department_id,
        nr.updated_at, nr.status,
        CONCAT('non_rs_', CAST(nr.id AS TEXT)) AS grouping_id,
        nr.status AS root_status,
        NULL AS assigned_to_user_id,
        (SELECT JSON_AGG(user_id) FROM (SELECT user_id FROM non_requisition_approvers WHERE non_requisition_id = nr.id UNION SELECT alt_approver_id AS user_id FROM non_requisition_approvers WHERE non_requisition_id = nr.id AND alt_approver_id IS NOT NULL) AS approvers) AS approvers
      FROM non_requisitions nr
    )
  `;

    const docTypeCustomOrderMap = {
      requisition: 1,
      canvass: 2,
      purchase_order: 3,
      invoice: 4,
      delivery_receipt: 5,
      payment_request: 6,
      non_requisition: 7,
    };

    const getDocTypeCaseOrderSql = () => {
      let caseSql = `CASE ud.doc_type `;
      for (const rawType in docTypeCustomOrderMap) {
        caseSql += `WHEN '${rawType}' THEN ${docTypeCustomOrderMap[rawType]} `;
      }
      caseSql += `ELSE 99 END`;
      return caseSql;
    };

    let orderClauseSql = '';

    if (sortColumn) {
      let primarySortExpression = '';
      let primarySortDirection = sortDirection;

      switch (sortColumn) {
        case 'ref_number':
          primarySortExpression = `
            SUBSTRING(ud.ref_number FROM 1 FOR 3),
            CAST(SUBSTRING(ud.ref_number FROM 4 FOR POSITION('AA' IN ud.ref_number) - 4) AS INT),
            CAST(SUBSTRING(ud.ref_number FROM POSITION('AA' IN ud.ref_number) + 2) AS INT)
          `;
          break;
        case 'doc_type':
          // Sort by the custom order for doc_type
          orderClauseSql = `ORDER BY ${getDocTypeCaseOrderSql()} ${sortDirection}, ud.id ASC`;
          break;
        case 'requestor':
          primarySortExpression = "CONCAT(u.first_name, ' ', u.last_name)";
          break;
        case 'company':
          primarySortExpression = 'c.name';
          break;
        case 'project':
          primarySortExpression = 'p.name';
          break;
        case 'department':
          primarySortExpression = 'd.name';
          break;
        case 'updatedAt':
        case 'updated_at':
          primarySortExpression = 'ud.updated_at';
          break;
        case 'status':
          primarySortExpression = 'ud.status';
          break;
        default:
          primarySortExpression = 'ud.id';
          primarySortDirection = 'ASC';
      }

      if (sortColumn !== 'doc_type') {
        orderClauseSql = `ORDER BY ${getDocTypeCaseOrderSql()} ASC, ${primarySortExpression} ${primarySortDirection}, ud.id ASC`;
      }
    } else {
      orderClauseSql = `
      ORDER BY
        CASE WHEN ud.root_status = 'closed' THEN 2 ELSE 1 END ASC,
        MAX(ud.updated_at) OVER (PARTITION BY ud.grouping_id) DESC,
        ud.grouping_id ASC,
        ${getDocTypeCaseOrderSql()} ASC,
        ud.updated_at DESC,
        ud.id ASC
      `;
    }

    orderClauseSql += ` LIMIT :limit OFFSET :offset`;

    const getBaseQuery = () => `
    ${unifiedDocsCTE}
    SELECT
      ud.id, ud.doc_type, ud.ref_number, ud.requestor_id,
      CONCAT(u.first_name, ' ', u.last_name) AS requestor_name,
      ud.company_id, c.name AS company_name,
      ud.project_id, p.name AS project_name,
      ud.department_id, d.name AS department_name,
      ud.updated_at, ud.status, ud.approvers, ud.grouping_id,
      ud.root_status,
      ud.assigned_to_user_id,
      CONCAT(assignee_u.first_name, ' ', assignee_u.last_name) AS assigned_to_user_name
    FROM unified_docs ud
    LEFT JOIN users u ON ud.requestor_id = u.id
    LEFT JOIN companies c ON ud.company_id = c.id
    LEFT JOIN projects p ON ud.project_id = p.id
    LEFT JOIN departments d ON ud.department_id = d.id
    LEFT JOIN users assignee_u ON ud.assigned_to_user_id = assignee_u.id
    WHERE 1=1
  `;

    const getCountQueryBase = () => `
    ${unifiedDocsCTE}
    SELECT COUNT(*) AS total FROM unified_docs ud
    LEFT JOIN users u ON ud.requestor_id = u.id
    LEFT JOIN companies c ON ud.company_id = c.id
    LEFT JOIN projects p ON ud.project_id = p.id
    LEFT JOIN departments d ON ud.department_id = d.id
    LEFT JOIN users assignee_u ON ud.assigned_to_user_id = assignee_u.id
    WHERE 1=1
  `;

    const getCountReplacements = (queryReplacements) => {
      const countR = { ...queryReplacements };
      delete countR.limit;
      delete countR.offset;
      return countR;
    };

    let resultData = {
      my_request: [],
      my_approval: [],
      all: [],
    };
    let totalCounts = {
      myRequestsTotal: 0,
      myApprovalsTotal: 0,
      allTotal: 0,
    };

    if (requestType === 'my_request' || requestType === undefined) {
      const myRequestsReplacements = { ...replacements };

      let myRequestsQuerySql = getBaseQuery();
      myRequestsQuerySql += ` AND ud.requestor_id = :userId AND (ud.doc_type = 'requisition' OR ud.doc_type = 'non_requisition')`;

      if (baseFilterClause) {
        myRequestsQuerySql += ` ${baseFilterClause}`;
      }

      myRequestsQuerySql += ` ${orderClauseSql}`;

      let myRequestsCountQuerySql = getCountQueryBase();
      myRequestsCountQuerySql += ` AND ud.requestor_id = :userId AND (ud.doc_type = 'requisition' OR ud.doc_type = 'non_requisition') `;

      if (baseFilterClause) {
        myRequestsCountQuerySql += ` ${baseFilterClause}`;
      }

      resultData.my_request = await this.db.sequelize.query(
        myRequestsQuerySql,
        {
          replacements: myRequestsReplacements,
          type: this.db.Sequelize.QueryTypes.SELECT,
        },
      );
      const [myRequestsTotalResult] = await this.db.sequelize.query(
        myRequestsCountQuerySql,
        {
          replacements: getCountReplacements(myRequestsReplacements),
          type: this.db.Sequelize.QueryTypes.SELECT,
        },
      );
      totalCounts.myRequestsTotal = parseInt(myRequestsTotalResult.total || 0);
    }

    if (requestType === 'my_approval' || requestType === undefined) {
      const myApprovalsReplacements = { ...replacements };
      let approvalConditions;
      if (
        role.name === 'Purchasing Staff' ||
        role.name === 'Purchasing Head' ||
        role.name === 'Purchasing Admin'
      ) {
        approvalConditions = `
     AND (
        (ud.doc_type = 'requisition' AND ud.status = 'assigning' AND ud.status !='rs_draft') OR
        (ud.assigned_to_user_id = :userId) OR
        (ud.doc_type = 'requisition' AND EXISTS (SELECT 1 FROM requisition_approvers ra WHERE ud.id = ra.requisition_id AND (ra.approver_id = :userId OR ra.alt_approver_id = :userId))) OR
        (ud.doc_type = 'canvass' AND EXISTS (SELECT 1 FROM canvass_approvers ca WHERE ud.id = ca.canvass_requisition_id AND (ca.user_id = :userId OR ca.alt_approver_id = :userId))) OR
        (ud.doc_type = 'purchase_order' AND EXISTS (SELECT 1 FROM purchase_order_approvers poa WHERE ud.id = poa.purchase_order_id AND (poa.user_id = :userId OR poa.alt_approver_id = :userId))) OR
        (ud.doc_type = 'payment_request' AND EXISTS (SELECT 1 FROM rs_payment_request_approvers pra WHERE ud.id = pra.payment_request_id AND (pra.user_id = :userId OR pra.alt_approver_id = :userId))) OR
        (ud.doc_type = 'non_requisition' AND EXISTS (SELECT 1 FROM non_requisition_approvers nra WHERE ud.id = nra.non_requisition_id AND (nra.user_id = :userId OR nra.alt_approver_id = :userId)))
      )
    `;
      } else {
        approvalConditions = ` AND (
        (ud.doc_type = 'requisition' AND ud.status !='rs_draft' AND EXISTS (SELECT 1 FROM requisition_approvers ra WHERE ud.id = ra.requisition_id AND (ra.approver_id = :userId OR ra.alt_approver_id = :userId))) OR
        (ud.doc_type = 'canvass' AND EXISTS (SELECT 1 FROM canvass_approvers ca WHERE ud.id = ca.canvass_requisition_id AND (ca.user_id = :userId OR ca.alt_approver_id = :userId))) OR
        (ud.doc_type = 'purchase_order' AND EXISTS (SELECT 1 FROM purchase_order_approvers poa WHERE ud.id = poa.purchase_order_id AND (poa.user_id = :userId OR poa.alt_approver_id = :userId))) OR
        (ud.doc_type = 'payment_request' AND EXISTS (SELECT 1 FROM rs_payment_request_approvers pra WHERE ud.id = pra.payment_request_id AND (pra.user_id = :userId OR pra.alt_approver_id = :userId))) OR
        (ud.doc_type = 'non_requisition' AND EXISTS (SELECT 1 FROM non_requisition_approvers nra WHERE ud.id = nra.non_requisition_id AND (nra.user_id = :userId OR nra.alt_approver_id = :userId)))
      )
    `;
      }

      let myApprovalsQuerySql = getBaseQuery();
      myApprovalsQuerySql += approvalConditions;

      if (baseFilterClause) {
        myApprovalsQuerySql += ` ${baseFilterClause}`;
      }

      myApprovalsQuerySql += ` ${orderClauseSql}`;

      let myApprovalsCountQuerySql = getCountQueryBase();
      myApprovalsCountQuerySql += approvalConditions;

      if (baseFilterClause) {
        myApprovalsCountQuerySql += ` ${baseFilterClause}`;
      }

      resultData.my_approval = await this.db.sequelize.query(
        myApprovalsQuerySql,
        {
          replacements: myApprovalsReplacements,
          type: this.db.Sequelize.QueryTypes.SELECT,
        },
      );

      const [myApprovalsTotalResult] = await this.db.sequelize.query(
        myApprovalsCountQuerySql,
        {
          replacements: getCountReplacements(myApprovalsReplacements),
          type: this.db.Sequelize.QueryTypes.SELECT,
        },
      );
      totalCounts.myApprovalsTotal = parseInt(
        myApprovalsTotalResult.total || 0,
      );
    }

    if (requestType === 'all' || requestType === undefined) {
      const allDocsReplacements = { ...replacements };

      let allDocsQuerySql = getBaseQuery();

      if (baseFilterClause) {
        allDocsQuerySql += ` ${baseFilterClause}`;
      }
      allDocsQuerySql += ` ${orderClauseSql}`;

      let allDocsCountQuerySql = getCountQueryBase();
      if (baseFilterClause) {
        allDocsCountQuerySql += ` ${baseFilterClause}`;
      }

      const allDocsCountReplacements =
        getCountReplacements(allDocsReplacements);

      resultData.all = await this.db.sequelize.query(allDocsQuerySql, {
        replacements: allDocsReplacements,
        type: this.db.Sequelize.QueryTypes.SELECT,
      });

      const [allDocsTotalResult] = await this.db.sequelize.query(
        allDocsCountQuerySql,
        {
          replacements: allDocsCountReplacements,
          type: this.db.Sequelize.QueryTypes.SELECT,
        },
      );

      totalCounts.allTotal = parseInt(allDocsTotalResult.total || 0);
    }

    const docTypeOutputMap = {
      requisition: 'R.S.',
      canvass: 'Canvass',
      purchase_order: 'Order',
      delivery_receipt: 'Delivery',
      invoice: 'Invoice',
      payment_request: 'Voucher',
      non_requisition: 'Non-R.S.',
    };

    const response = {
      my_request:
        requestType === 'my_request' || requestType === undefined
          ? resultData.my_request.map((item) => ({
              ...item,
              doc_type: docTypeOutputMap[item.doc_type] || item.doc_type,
            }))
          : [],
      my_approval:
        requestType === 'my_approval' || requestType === undefined
          ? resultData.my_approval.map((item) => {
              const mappedItem = {
                ...item,
                doc_type: docTypeOutputMap[item.doc_type] || item.doc_type,
              };
              return mappedItem;
            })
          : [],
      all:
        requestType === 'all' || requestType === undefined
          ? resultData.all.map((item) => ({
              ...item,
              doc_type: docTypeOutputMap[item.doc_type] || item.doc_type,
            }))
          : [],
      meta: {
        message: 'Successfully retrieved dashboard data',
        page: parseInt(page),
        limit: parseInt(limit),
        myRequestsTotal: totalCounts.myRequestsTotal,
        myRequestsTotalPages: Math.ceil(totalCounts.myRequestsTotal / limit),
        myApprovalsTotal: totalCounts.myApprovalsTotal,
        myApprovalsTotalPages: Math.ceil(totalCounts.myApprovalsTotal / limit),
        allTotal: totalCounts.allTotal,
        allTotalPages: Math.ceil(totalCounts.allTotal / limit),
      },
    };
    return response;
  }

  async getById(requisitionId, options = {}) {
    const requisition = await this.tableName.findByPk(requisitionId, {
      include: [
        {
          association: 'canvassRequisitions',
          as: 'canvassRequisitions',
          attributes: ['id'],
        },
        {
          association: 'requisitionItemLists',
          as: 'requisitionItemLists',
        },
      ],
      ...options,
    });

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with id of ${requisitionId} not found`,
      });
    }

    return requisition?.get({ plain: true });
  }

  async assignRequisition(requisitionId, assignedTo, type, userFromToken) {
    const STATUS = 'assigned';

    const { REQUISITION_STATUS } = this.constants.requisition;

    const ROLE_CONFIGS = {
      update: {
        validRoles: ['Purchasing Head'],
        validStatuses: [
          'assigned',
          'partially_canvassed',
          'canvass_approval',
          'for_delivery',
          'purchase_order',
          'purchase_order_approval',
          'returned',
          'delivered',
          'paying',
        ],
      },
      default: {
        validRoles: ['Purchasing Staff', 'Purchasing Head'],
        validStatuses: ['assigning', 'approved'],
        validStatuses: [
          REQUISITION_STATUS.ASSIGNING,
          REQUISITION_STATUS.APPROVED,
        ],
      },
    };

    const config = ROLE_CONFIGS[type] || ROLE_CONFIGS.default;

    const [requisition, users] = await Promise.all([
      this.tableName.findOne({
        where: { id: requisitionId },
        attributes: ['id', 'status'],
      }),
      this.db.userModel.findAll({
        where: {
          id: type === 'update' ? [assignedTo, userFromToken] : assignedTo,
        },
        include: [
          {
            model: this.db.roleModel,
            as: 'role',
            attributes: ['name'],
          },
        ],
        attributes: ['id'],
      }),
    ]);

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with id of ${requisitionId} not found`,
      });
    }

    if (
      requisition.status === REQUISITION_STATUS.DRAFT ||
      requisition.status === REQUISITION_STATUS.CLOSED ||
      requisition.status === REQUISITION_STATUS.SUBMITTED
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Requisition with id of ${requisitionId} cannot be assigned`,
      });
    }

    const [assignedUser, tokenUser] = [
      users.find((user) => user.id === assignedTo),
      type === 'update'
        ? users.find((user) => user.id === userFromToken)
        : null,
    ];

    if (!assignedUser) {
      throw this.clientErrors.NOT_FOUND({
        message: `User with id of ${assignedTo} not found`,
      });
    }

    if (type === 'update') {
      if (!tokenUser) {
        throw this.clientErrors.NOT_FOUND({
          message: `User from token with id of ${userFromToken} not found`,
        });
      }
      if (!config.validRoles.includes(tokenUser.role.name)) {
        throw this.clientErrors.UNAUTHORIZED({
          message: `User is not authorized to assign requisition`,
        });
      }
    } else if (!config.validRoles.includes(assignedUser.role.name)) {
      throw this.clientErrors.UNAUTHORIZED({
        message: `User is not authorized to assign requisition`,
      });
    }

    const updateData = { assignedTo };
    if (requisition.status === REQUISITION_STATUS.ASSIGNING) {
      updateData.status = STATUS;
    }
    return this.update({ id: requisitionId }, updateData);
  }

  async getCanvassRequisition(requisitionId, options = {}) {
    const whereClause = { id: requisitionId };
    const { sortBy, filterBy } = options;
    const { CANVASS_STATUS } = this.constants.canvass;
    const { canvassSortSchema, canvassFilterSchema } = this.entities.canvass;

    const parsedSortBy = canvassSortSchema.parse(sortBy);
    const parsedFilterBy = canvassFilterSchema.parse(filterBy);

    const filterByWhereClause =
      this.utils.buildFilterWhereClause(parsedFilterBy);

    if (filterByWhereClause?.canvassNumber) {
      const searchValue = filterByWhereClause.canvassNumber;
      whereClause[this.Sequelize.Op.or] = [
        this.Sequelize.literal(`
          CASE 
            WHEN "canvassRequisitions"."status" = '${CANVASS_STATUS.DRAFT}' 
            THEN CONCAT('CS-TMP-', company_code, "canvassRequisitions"."cs_letter", "canvassRequisitions"."draft_cs_number")
            ELSE CONCAT('CS-', company_code, "canvassRequisitions"."cs_letter", "canvassRequisitions"."cs_number")
          END ILIKE '%${searchValue}%'
        `),
      ];
    }

    const orderClauses = parsedSortBy?.map(([field, direction]) => {
      switch (field) {
        case 'status':
          return [
            this.db.Sequelize.literal('"canvassRequisitions"."status"'),
            direction,
          ];
        case 'createdAt':
          return [
            this.db.Sequelize.literal('"canvassRequisitions"."created_at"'),
            direction,
          ];
        case 'updatedAt':
          return [
            this.db.Sequelize.literal('"canvassRequisitions"."updated_at"'),
            direction,
          ];
        case 'canvassNumber':
          return [
            this.db.Sequelize.literal('"canvassRequisitions"."cs_number"'),
            direction,
          ];
        case 'lastApprover':
          return [
            this.db.Sequelize.literal(
              `(SELECT "users"."first_name" FROM "canvass_approvers" 
                  LEFT JOIN "users" ON "users"."id" = "canvass_approvers"."user_id"
                  WHERE "canvass_approvers"."canvass_requisition_id" = "canvassRequisitions"."id"
                  AND "canvass_approvers"."status" = 'pending'
                  ORDER BY "canvass_approvers"."level" ASC, "canvass_approvers"."is_adhoc" ASC
                  LIMIT 1)`,
            ),
            direction,
          ];
        default:
          return [
            this.db.Sequelize.literal(`"canvassRequisitions"."${field}"`),
            direction,
          ];
      }
    });

    const requisition = await this.findOne({
      where: whereClause,
      nest: true,
      attributes: [
        'id',
        'assignedTo',
        'status',
        'type',
        'dateRequired',
        'companyCode',
        [
          this.Sequelize.literal(
            "CONCAT('RS-', company_code, rs_letter, rs_number)",
          ),
          'rsNumber',
        ],
        [
          this.Sequelize.literal(
            '(SELECT COUNT(*) FROM "canvass_requisitions" WHERE "requisition_id" = "requisitions"."id")',
          ),
          'total',
        ],
      ],
      order: orderClauses,
      include: [
        {
          required: false,
          as: 'canvass',
          association: 'canvassRequisitions',
          attributes: [
            'id',
            'status',
            'csNumber',
            'csLetter',
            'draftCsNumber',
            'updatedAt',
            'createdAt',
          ],
          include: [
            {
              required: false,
              association: 'canvassApprovers',
              as: 'latestApprover',
              attributes: ['id', 'userId', 'level', 'status', 'isAdhoc'],
              where: {
                status: 'pending',
              },
              order: [
                ['level', 'ASC'],
                ['isAdhoc', 'ASC'],
              ],
              limit: 1,
              include: [
                {
                  required: false,
                  association: 'approver',
                  as: 'approver',
                  attributes: ['id', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          required: false,
          as: 'assignee',
          association: 'assignee',
          attributes: ['id', 'firstName', 'lastName'],
          include: [
            {
              as: 'role',
              association: 'role',
              attributes: ['name'],
            },
          ],
        },
      ],
    });

    return requisition;
  }

  async cancelRequisition(request) {
    const { userFromToken } = request;
    const { requisitionId } = request.params;
    const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
      this.constants.notification;
    const { CANVASS_STATUS } = this.constants.canvass;
    const { PO_STATUS } = this.constants.purchaseOrder;

    const requisition = await this.tableName.findOne({
      where: { id: requisitionId },
      attributes: [
        'id',
        'status',
        'type',
        'createdBy',
        [
          this.Sequelize.fn(
            'CONCAT',
            this.Sequelize.literal(
              "CASE WHEN \"requisitions\".\"status\" = 'rs_draft' THEN 'TMP-' ELSE '' END",
            ),
            this.Sequelize.col('requisitions.company_code'),
            this.Sequelize.col('rs_letter'),
            this.Sequelize.fn(
              'COALESCE',
              this.Sequelize.col('rs_number'),
              this.Sequelize.col('draft_rs_number'),
            ),
          ),
          'rsNumber',
        ],
      ],
      include: [
        {
          model: this.db.requisitionItemListModel,
          as: 'requisitionItemLists',
          where: {
            itemType: ['ofm', 'ofm-tom'],
          },
          required: false,
          attributes: ['id', 'itemId', 'quantity'],
        },
      ],
    });

    const isNotCancellable = [
      'rs_draft',
      'cancelled',
      'for_delivery',
      'for_po_review',
      'for_pr_approval',
    ].includes(requisition.status);

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with id of ${requisitionId} not found`,
      });
    }

    if (isNotCancellable) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Cannot cancel requisition due to status`,
      });
    }

    const t = await this.db.sequelize.transaction();

    try {
      await this.update(
        { id: requisitionId },
        { status: 'cancelled' },
        { transaction: t },
      );

      // update canvass status to cancel when requisition is cancelled
      await this.canvassRequisitionRepository.update(
        { requisitionId: requisitionId },
        { status: CANVASS_STATUS.CANCELLED },
        { transaction: t },
      );

      // fetch the purchase order to cancel using requisition id
      const purchaseOrder = await this.purchaseOrderRepository.findOne({
        where: {
          requisitionId: requisitionId,
        },
      });

      // cancel purchase order when requisition slip is cancelled
      if (purchaseOrder) {
        await this.purchaseOrderRepository.update(
          { requisitionId },
          { status: PO_STATUS.CANCELLED_PO },
          { transaction: t },
        );
      }

      // If the Items in the Requisition is an OFM Item, should return the Quantity into the GFQ
      if (
        ['ofm', 'ofm-tom'].includes(requisition.type) &&
        requisition.requisitionItemLists?.length > 0
      ) {
        const itemUpdates = requisition.requisitionItemLists
          .filter((item) => item.itemId != null)
          .map((item) => ({
            id: item.itemId,
            quantity: item.quantity,
          }));

        if (itemUpdates.length > 0) {
          await Promise.all(
            itemUpdates.map((update) =>
              this.db.itemModel.increment('remaining_gfq', {
                by: update.quantity,
                where: { id: update.id },
                transaction: t,
              }),
            ),
          );
        }
      }

      await this.notificationService.sendNotification({
        title: NOTIFICATION_DETAILS.CANCEL_RS.title,
        message: NOTIFICATION_DETAILS.CANCEL_RS.message(
          `${requisition.rsNumber}`,
        ),
        type: NOTIFICATION_TYPES.REQUISITION_SLIP,
        recipientUserIds: [requisition.createdBy],
        metaData: {
          requisitionId: requisition.id,
        },
        senderId: userFromToken.id,
      });

      await t.commit();
      return true;
    } catch (error) {
      await t.rollback();
      throw error;
    }
  }

  async updateRSForBadge({ rsIds, transaction }) {
    return await this.tableName.update(
      { updatedAt: new Date() },
      { where: { id: rsIds } },
      { transaction },
    );
  }
}

module.exports = RequisitionRepository;
